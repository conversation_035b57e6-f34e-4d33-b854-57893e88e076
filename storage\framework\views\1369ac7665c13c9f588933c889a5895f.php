<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'सदस्य डैशबोर्ड'); ?> - छत्तीसगढ़ यादव समाज</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Hind:wght@300;400;500;600;700&family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    
    <style>
        [x-cloak] { display: none !important; }
    </style>
</head>
<body class="h-full font-hindi antialiased bg-gray-50" x-data="{
    darkMode: false
}" x-init="
    // Initialize theme - default to light mode
    const savedTheme = localStorage.getItem('memberDarkMode');
    if (savedTheme === null) {
        localStorage.setItem('memberDarkMode', 'false');
        darkMode = false;
    } else {
        darkMode = savedTheme === 'true';
    }

    // Watch for theme changes
    $watch('darkMode', val => {
        localStorage.setItem('memberDarkMode', val.toString());
        if (val) {
            document.documentElement.classList.add('dark');
            document.body.classList.remove('bg-gray-50');
            document.body.classList.add('bg-navy-900');
        } else {
            document.documentElement.classList.remove('dark');
            document.body.classList.remove('bg-navy-900');
            document.body.classList.add('bg-gray-50');
        }
    });

    // Apply initial theme
    if (darkMode) {
        document.documentElement.classList.add('dark');
        document.body.classList.remove('bg-gray-50');
        document.body.classList.add('bg-navy-900');
    } else {
        document.documentElement.classList.remove('dark');
        document.body.classList.remove('bg-navy-900');
        document.body.classList.add('bg-gray-50');
    }
">
    
    <!-- Flash Messages -->
    <?php if(session('success')): ?>
        <div id="flash-success" class="fixed top-4 right-4 z-50 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg max-w-sm">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <?php echo e(session('success')); ?>

            </div>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div id="flash-error" class="fixed top-4 right-4 z-50 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg max-w-sm">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <?php echo e(session('error')); ?>

            </div>
        </div>
    <?php endif; ?>

    <?php if(session('warning')): ?>
        <div id="flash-warning" class="fixed top-4 right-4 z-50 bg-yellow-500 text-white px-6 py-3 rounded-lg shadow-lg max-w-sm">
            <div class="flex items-center">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <?php echo e(session('warning')); ?>

            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="h-full">
        <?php echo $__env->yieldContent('content'); ?>
    </main>



    <script>
        // Auto-hide flash messages
        setTimeout(() => {
            const flashMessages = document.querySelectorAll('[id^="flash-"]');
            flashMessages.forEach(message => {
                message.style.opacity = '0';
                message.style.transform = 'translateX(100%)';
                message.style.transition = 'all 0.3s ease-in-out';
                setTimeout(() => message.remove(), 300);
            });
        }, 5000);

        // Add click to dismiss flash messages
        document.querySelectorAll('[id^="flash-"]').forEach(message => {
            message.addEventListener('click', () => {
                message.style.opacity = '0';
                message.style.transform = 'translateX(100%)';
                message.style.transition = 'all 0.3s ease-in-out';
                setTimeout(() => message.remove(), 300);
            });
        });
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\web-yadav-samaj\resources\views/layouts/member.blade.php ENDPATH**/ ?>