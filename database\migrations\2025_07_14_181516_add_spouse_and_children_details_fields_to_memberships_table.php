<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('memberships', function (Blueprint $table) {
            // Add spouse details fields (for when marital_status is 'विवाहित')
            $table->string('spouse_name')->nullable()->after('vivah_tithi');
            $table->string('spouse_education')->nullable()->after('spouse_name');
            $table->text('spouse_education_details')->nullable()->after('spouse_education');
            $table->text('spouse_work_details')->nullable()->after('spouse_education_details');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('memberships', function (Blueprint $table) {
            // Drop the spouse details fields
            $table->dropColumn([
                'spouse_name',
                'spouse_education',
                'spouse_education_details',
                'spouse_work_details'
            ]);
        });
    }
};
