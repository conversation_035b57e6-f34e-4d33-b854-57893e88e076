<?php $__env->startSection('title', 'पारिवारिक वंशावली'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-white dark:bg-navy-900">
    <div class="px-4 sm:px-6 lg:px-8 py-6">
        <!-- Simple Header -->
        <div class="mb-6 no-print">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        पारिवारिक वंशावली
                    </h1>
                    <p class="mt-1 text-gray-600 dark:text-gray-400">
                        <?php echo e($member->name); ?> का पूर्वजों का विवरण
                    </p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="window.print()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        प्रिंट करें
                    </button>
                    <a href="<?php echo e(route('member.dashboard')); ?>" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                        वापस
                    </a>
                </div>
            </div>
        </div>

        <?php if($member->ancestors && count($member->ancestors) > 0): ?>
            <!-- Simple Family Tree -->
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg">
                <div class="p-6">
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                        पारिवारिक वंशावली चार्ट
                    </h2>
                    <?php
                        // Sort ancestors by generation level (highest to lowest)
                        $sortedAncestors = collect($member->ancestors)->sortByDesc('generation_level');
                        $generationLabels = [
                            7 => '7वीं पीढ़ी',
                            6 => '6वीं पीढ़ी',
                            5 => '5वीं पीढ़ी',
                            4 => '4वीं पीढ़ी',
                            3 => '3वीं पीढ़ी',
                            2 => '2वीं पीढ़ी (दादा-दादी)',
                            1 => '1वीं पीढ़ी (माता-पिता)'
                        ];
                    ?>

                    <?php $__currentLoopData = $generationLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $ancestorsInLevel = $sortedAncestors->where('generation_level', $level);
                        ?>
                        <?php if($ancestorsInLevel->count() > 0): ?>
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 pb-2 border-b border-gray-300 dark:border-navy-600">
                                    <?php echo e($label); ?>

                                </h3>
                                        
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    <?php $__currentLoopData = $ancestorsInLevel; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ancestor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="border border-gray-300 dark:border-navy-600 rounded p-4 bg-gray-50 dark:bg-navy-700">
                                            <h4 class="font-bold text-lg text-gray-900 dark:text-white mb-2">
                                                <?php echo e($ancestor['name'] ?? 'N/A'); ?>

                                            </h4>
                                                        
                                            <div class="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                                                <?php if(!empty($ancestor['birth_date'])): ?>
                                                    <div><strong>जन्म:</strong> <?php echo e($ancestor['birth_date']); ?></div>
                                                <?php endif; ?>
                                                <?php if(!empty($ancestor['death_date'])): ?>
                                                    <div><strong>मृत्यु:</strong> <?php echo e($ancestor['death_date']); ?></div>
                                                <?php endif; ?>
                                                <?php if(!empty($ancestor['birth_place'])): ?>
                                                    <div><strong>जन्म स्थान:</strong> <?php echo e($ancestor['birth_place']); ?></div>
                                                <?php endif; ?>
                                                <?php if(!empty($ancestor['occupation'])): ?>
                                                    <div><strong>व्यवसाय:</strong> <?php echo e($ancestor['occupation']); ?></div>
                                                <?php endif; ?>
                                                <?php if(!empty($ancestor['spouse'])): ?>
                                                    <div><strong>पत्नी:</strong> <?php echo e($ancestor['spouse']); ?></div>
                                                <?php endif; ?>
                                                <?php if(!empty($ancestor['children_count'])): ?>
                                                    <div><strong>संतान:</strong> <?php echo e($ancestor['children_count']); ?></div>
                                                <?php endif; ?>
                                                <?php if(!empty($ancestor['gotra'])): ?>
                                                    <div><strong>गोत्र:</strong> <?php echo e($ancestor['gotra']); ?></div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            
                    <!-- Current Member -->
                    <div class="mt-8 pt-6 border-t-2 border-blue-500">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                            वर्तमान सदस्य
                        </h3>
                        <div class="border border-blue-300 rounded p-4 bg-blue-50 dark:bg-blue-900/20">
                            <h4 class="font-bold text-lg text-blue-900 dark:text-blue-100 mb-2">
                                <?php echo e($member->name); ?>

                            </h4>
                            <div class="text-sm space-y-1 text-blue-700 dark:text-blue-300">
                                <div><strong>पिता:</strong> <?php echo e($member->fathers_husband_name); ?></div>
                                <?php if($member->birth_date): ?>
                                    <div><strong>जन्म:</strong> <?php echo e($member->birth_date->format('d/m/Y')); ?></div>
                                <?php endif; ?>
                                <?php if($member->address): ?>
                                    <div><strong>पता:</strong> <?php echo e(Str::limit($member->address, 50)); ?></div>
                                <?php endif; ?>
                                <?php if($member->department_name): ?>
                                    <div><strong>विभाग:</strong> <?php echo e($member->department_name); ?></div>
                                <?php endif; ?>
                                <div><strong>सदस्यता:</strong> <?php echo e($member->membership_number); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- No Ancestors Message -->
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg">
                <div class="px-6 py-8 text-center">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">कोई पूर्वज जानकारी नहीं</h3>
                    <p class="mt-2 text-gray-500 dark:text-gray-400">
                        आपके सदस्यता आवेदन में पूर्वजों की जानकारी उपलब्ध नहीं है।
                    </p>
                </div>
            </div>
        <?php endif; ?>

        <!-- Children Section -->
        <?php if($member->children && count($member->children) > 0): ?>
            <div class="bg-white dark:bg-navy-800 border border-gray-200 dark:border-navy-700 rounded-lg mt-6">
                <div class="p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">
                        संतान विवरण
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php $__currentLoopData = $member->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-300 dark:border-navy-600 rounded p-3 bg-gray-50 dark:bg-navy-700">
                                <h4 class="font-bold text-gray-900 dark:text-white"><?php echo e($child['name'] ?? 'N/A'); ?></h4>
                                <div class="text-sm text-gray-600 dark:text-gray-300">
                                    <div><?php echo e($child['gender'] === 'male' ? 'पुत्र' : 'पुत्री'); ?></div>
                                    <?php if(!empty($child['dob'])): ?>
                                        <div>जन्म: <?php echo e(\Carbon\Carbon::parse($child['dob'])->format('d/m/Y')); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
    body {
        font-family: 'Noto Sans Devanagari', Arial, sans-serif;
    }

    @media print {
        .no-print {
            display: none !important;
        }

        body {
            background: white !important;
        }

        .bg-white {
            background: white !important;
        }

        .border {
            border: 1px solid #000 !important;
        }

        .text-gray-900 {
            color: #000 !important;
        }

        .text-gray-600 {
            color: #333 !important;
        }

        .bg-gray-50 {
            background: #f9f9f9 !important;
        }

        .bg-blue-50 {
            background: #e6f3ff !important;
        }

        .text-blue-900 {
            color: #1e3a8a !important;
        }

        .text-blue-700 {
            color: #1d4ed8 !important;
        }

        .border-blue-500 {
            border-color: #3b82f6 !important;
        }

        .border-blue-300 {
            border-color: #93c5fd !important;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.member', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web-yadav-samaj\resources\views/member/family-tree.blade.php ENDPATH**/ ?>