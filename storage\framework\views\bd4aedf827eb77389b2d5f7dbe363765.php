

<?php $__env->startSection('title', 'प्रोफाइल संपादित करें'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 dark:bg-navy-900">
    <!-- Mobile Header -->
    <div class="bg-white dark:bg-navy-800 shadow-sm border-b border-gray-200 dark:border-navy-700 lg:hidden">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="<?php echo e(route('member.profile')); ?>" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-navy-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900 dark:text-white">प्रोफाइल संपादित करें</h1>
                </div>
            </div>
        </div>
    </div>

    <div class="px-4 sm:px-6 lg:px-8 py-6">
        <!-- Desktop Back Button -->
        <div class="hidden lg:block mb-6">
            <a href="<?php echo e(route('member.profile')); ?>" class="inline-flex items-center text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                प्रोफाइल पर वापस जाएं
            </a>
        </div>

        <!-- Page Header -->
        <div class="mb-8">
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                प्रोफाइल संपादित करें
            </h1>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                अपनी व्यक्तिगत जानकारी को अपडेट करें
            </p>
        </div>

        <!-- Success/Error Messages -->
        <?php if(session('success')): ?>
            <div class="mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800 dark:text-green-200"><?php echo e(session('success')); ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <form action="<?php echo e(route('member.profile.update')); ?>" method="POST" enctype="multipart/form-data" class="space-y-8">
            <?php echo csrf_field(); ?>

            <!-- Basic Information Section -->
            <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">व्यक्तिगत जानकारी</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">नाम *</label>
                            <input type="text" name="name" id="name" value="<?php echo e(old('name', $member->name)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" required>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Father/Husband Name -->
                        <div>
                            <label for="fathers_husband_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">पिता/पति का नाम *</label>
                            <input type="text" name="fathers_husband_name" id="fathers_husband_name" value="<?php echo e(old('fathers_husband_name', $member->fathers_husband_name)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" required>
                            <?php $__errorArgs = ['fathers_husband_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Mobile Number -->
                        <div>
                            <label for="mobile_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">मोबाइल नंबर *</label>
                            <input type="tel" name="mobile_number" id="mobile_number" value="<?php echo e(old('mobile_number', $member->mobile_number)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200"
                                   pattern="[0-9]{10}" maxlength="10" required>
                            <?php $__errorArgs = ['mobile_number'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Birth Date -->
                        <div>
                            <label for="birth_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">जन्म तिथि</label>
                            <input type="date" name="birth_date" id="birth_date" value="<?php echo e(old('birth_date', $member->birth_date?->format('Y-m-d'))); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                            <?php $__errorArgs = ['birth_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Marital Status -->
                        <div>
                            <label for="marital_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">वैवाहिक स्थिति</label>
                            <select name="marital_status" id="marital_status"
                                    class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                <option value="">चुनें</option>
                                <option value="अविवाहित" <?php echo e(old('marital_status', $member->marital_status) == 'अविवाहित' ? 'selected' : ''); ?>>अविवाहित</option>
                                <option value="विवाहित" <?php echo e(old('marital_status', $member->marital_status) == 'विवाहित' ? 'selected' : ''); ?>>विवाहित</option>
                                <option value="विधवा" <?php echo e(old('marital_status', $member->marital_status) == 'विधवा' ? 'selected' : ''); ?>>विधवा</option>
                                <option value="तलाकशुदा" <?php echo e(old('marital_status', $member->marital_status) == 'तलाकशुदा' ? 'selected' : ''); ?>>तलाकशुदा</option>
                            </select>
                            <?php $__errorArgs = ['marital_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Family Members -->
                        <div>
                            <label for="family_members" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">परिवार के सदस्य</label>
                            <input type="number" name="family_members" id="family_members" value="<?php echo e(old('family_members', $member->family_members)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200"
                                   min="1" max="50">
                            <?php $__errorArgs = ['family_members'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Education -->
                        <div>
                            <label for="education" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">शिक्षा</label>
                            <input type="text" name="education" id="education" value="<?php echo e(old('education', $member->education)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                            <?php $__errorArgs = ['education'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Caste Details -->
                        <div>
                            <label for="caste_details" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">जाति विवरण</label>
                            <input type="text" name="caste_details" id="caste_details" value="<?php echo e(old('caste_details', $member->caste_details)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                            <?php $__errorArgs = ['caste_details'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Address -->
                    <div class="mt-6">
                        <label for="address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">पता</label>
                        <textarea name="address" id="address" rows="4"
                                  class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200 resize-vertical"><?php echo e(old('address', $member->address)); ?></textarea>
                        <?php $__errorArgs = ['address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>

            <!-- Professional Information Section -->
            <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">व्यावसायिक जानकारी</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Department Name -->
                        <div>
                            <label for="department_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">विभाग का नाम</label>
                            <input type="text" name="department_name" id="department_name" value="<?php echo e(old('department_name', $member->department_name)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                            <?php $__errorArgs = ['department_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Office -->
                        <div>
                            <label for="office" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">कार्यालय</label>
                            <input type="text" name="office" id="office" value="<?php echo e(old('office', $member->office)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                            <?php $__errorArgs = ['office'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Proposer Information Section -->
            <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">प्रस्तावक की जानकारी</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Member Name Signature -->
                        <div>
                            <label for="member_name_signature" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">सदस्य का नाम</label>
                            <input type="text" name="member_name_signature" id="member_name_signature" value="<?php echo e(old('member_name_signature', $member->member_name_signature)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                            <?php $__errorArgs = ['member_name_signature'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Mobile -->
                        <div>
                            <label for="mobile" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">मोबाइल</label>
                            <input type="tel" name="mobile" id="mobile" value="<?php echo e(old('mobile', $member->mobile)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200"
                                   pattern="[0-9]{10}" maxlength="10">
                            <?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Vibhag -->
                        <div>
                            <label for="vibhag" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">विभाग</label>
                            <input type="text" name="vibhag" id="vibhag" value="<?php echo e(old('vibhag', $member->vibhag)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                            <?php $__errorArgs = ['vibhag'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Proposer Address -->
                    <div class="mt-6">
                        <label for="proposer_address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">प्रस्तावक का पता</label>
                        <textarea name="proposer_address" id="proposer_address" rows="4"
                                  class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200 resize-vertical"><?php echo e(old('proposer_address', $member->proposer_address)); ?></textarea>
                        <?php $__errorArgs = ['proposer_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                </div>
            </div>

            <!-- File Upload Section -->
            <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">फोटो और हस्ताक्षर</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Photo Upload -->
                        <div>
                            <label for="photo" class="block text-sm font-medium text-gray-700 dark:text-gray-300">फोटो</label>
                            <?php if($member->photo): ?>
                                <div class="mt-2 mb-3">
                                    <img src="<?php echo e(Storage::url($member->photo)); ?>" alt="Current Photo" class="h-20 w-20 rounded-lg object-cover">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">वर्तमान फोटो</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" name="photo" id="photo" accept="image/jpeg,image/png,image/jpg"
                                   class="mt-1 block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-teal-50 file:text-teal-700 hover:file:bg-teal-100 dark:file:bg-teal-900 dark:file:text-teal-300">
                            <div id="photo-progress" class="hidden mt-2">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div id="photo-progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">अपलोड हो रहा है... <span id="photo-progress-text">0%</span></p>
                            </div>
                            <div id="photo-error" class="text-red-500 text-sm mt-2 hidden"></div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">JPG, PNG अधिकतम 2MB</p>
                            <?php $__errorArgs = ['photo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Signature Upload -->
                        <div>
                            <label for="signature" class="block text-sm font-medium text-gray-700 dark:text-gray-300">हस्ताक्षर</label>
                            <?php if($member->signature): ?>
                                <div class="mt-2 mb-3">
                                    <img src="<?php echo e(Storage::url($member->signature)); ?>" alt="Current Signature" class="h-16 w-32 rounded-lg object-cover border">
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">वर्तमान हस्ताक्षर</p>
                                </div>
                            <?php endif; ?>
                            <input type="file" name="signature" id="signature" accept="image/jpeg,image/png,image/jpg"
                                   class="mt-1 block w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-teal-50 file:text-teal-700 hover:file:bg-teal-100 dark:file:bg-teal-900 dark:file:text-teal-300">
                            <div id="signature-progress" class="hidden mt-2">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div id="signature-progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">अपलोड हो रहा है... <span id="signature-progress-text">0%</span></p>
                            </div>
                            <div id="signature-error" class="text-red-500 text-sm mt-2 hidden"></div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">JPG, PNG अधिकतम 1MB</p>
                            <?php $__errorArgs = ['signature'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Children Information Section -->
            <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">बच्चों की जानकारी</h3>
                </div>
                <div class="px-6 py-4">
                    <div id="children-container">
                        <?php if($member->children && count($member->children) > 0): ?>
                            <?php $__currentLoopData = $member->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="child-entry border border-gray-200 dark:border-navy-600 rounded-lg p-4 mb-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">बच्चा <?php echo e($index + 1); ?></h4>
                                        <button type="button" class="remove-child text-red-600 hover:text-red-800 text-sm">हटाएं</button>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">नाम</label>
                                            <input type="text" name="children[<?php echo e($index); ?>][name]" value="<?php echo e(old('children.'.$index.'.name', $child['name'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">लिंग</label>
                                            <select name="children[<?php echo e($index); ?>][gender]"
                                                    class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                                <option value="">चुनें</option>
                                                <option value="पुत्र" <?php echo e(old('children.'.$index.'.gender', $child['gender'] ?? '') == 'पुत्र' ? 'selected' : ''); ?>>पुत्र</option>
                                                <option value="पुत्री" <?php echo e(old('children.'.$index.'.gender', $child['gender'] ?? '') == 'पुत्री' ? 'selected' : ''); ?>>पुत्री</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">जन्म तिथि</label>
                                            <input type="date" name="children[<?php echo e($index); ?>][dob]" value="<?php echo e(old('children.'.$index.'.dob', $child['dob'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                    <button type="button" id="add-child" class="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-navy-600 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 hover:bg-gray-50 dark:hover:bg-navy-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        बच्चा जोड़ें
                    </button>
                </div>
            </div>

            <!-- Vanshavali (Ancestors) Information Section -->
            <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">पारिवारिक वंशावली</h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">अपने पूर्वजों की जानकारी जोड़ें</p>
                </div>
                <div class="px-6 py-4">
                    <div id="ancestors-container">
                        <?php if($member->ancestors && count($member->ancestors) > 0): ?>
                            <?php $__currentLoopData = $member->ancestors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $ancestor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="ancestor-entry border border-gray-200 dark:border-navy-600 rounded-lg p-4 mb-4">
                                    <div class="flex justify-between items-center mb-3">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">पूर्वज <?php echo e($index + 1); ?></h4>
                                        <button type="button" class="remove-ancestor text-red-600 hover:text-red-800 text-sm">हटाएं</button>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">नाम *</label>
                                            <input type="text" name="ancestors[<?php echo e($index); ?>][name]" value="<?php echo e(old('ancestors.'.$index.'.name', $ancestor['name'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" required>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">पीढ़ी स्तर *</label>
                                            <select name="ancestors[<?php echo e($index); ?>][generation_level]"
                                                    class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" required>
                                                <option value="">चुनें</option>
                                                <option value="1" <?php echo e(old('ancestors.'.$index.'.generation_level', $ancestor['generation_level'] ?? '') == '1' ? 'selected' : ''); ?>>1वीं पीढ़ी (माता-पिता)</option>
                                                <option value="2" <?php echo e(old('ancestors.'.$index.'.generation_level', $ancestor['generation_level'] ?? '') == '2' ? 'selected' : ''); ?>>2वीं पीढ़ी (दादा-दादी)</option>
                                                <option value="3" <?php echo e(old('ancestors.'.$index.'.generation_level', $ancestor['generation_level'] ?? '') == '3' ? 'selected' : ''); ?>>3वीं पीढ़ी</option>
                                                <option value="4" <?php echo e(old('ancestors.'.$index.'.generation_level', $ancestor['generation_level'] ?? '') == '4' ? 'selected' : ''); ?>>4वीं पीढ़ी</option>
                                                <option value="5" <?php echo e(old('ancestors.'.$index.'.generation_level', $ancestor['generation_level'] ?? '') == '5' ? 'selected' : ''); ?>>5वीं पीढ़ी</option>
                                                <option value="6" <?php echo e(old('ancestors.'.$index.'.generation_level', $ancestor['generation_level'] ?? '') == '6' ? 'selected' : ''); ?>>6वीं पीढ़ी</option>
                                                <option value="7" <?php echo e(old('ancestors.'.$index.'.generation_level', $ancestor['generation_level'] ?? '') == '7' ? 'selected' : ''); ?>>7वीं पीढ़ी</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">जन्म तिथि</label>
                                            <input type="date" name="ancestors[<?php echo e($index); ?>][birth_date]" value="<?php echo e(old('ancestors.'.$index.'.birth_date', $ancestor['birth_date'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">मृत्यु तिथि</label>
                                            <input type="date" name="ancestors[<?php echo e($index); ?>][death_date]" value="<?php echo e(old('ancestors.'.$index.'.death_date', $ancestor['death_date'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">जन्म स्थान</label>
                                            <input type="text" name="ancestors[<?php echo e($index); ?>][birth_place]" value="<?php echo e(old('ancestors.'.$index.'.birth_place', $ancestor['birth_place'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">व्यवसाय</label>
                                            <input type="text" name="ancestors[<?php echo e($index); ?>][occupation]" value="<?php echo e(old('ancestors.'.$index.'.occupation', $ancestor['occupation'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">पत्नी/पति का नाम</label>
                                            <input type="text" name="ancestors[<?php echo e($index); ?>][spouse]" value="<?php echo e(old('ancestors.'.$index.'.spouse', $ancestor['spouse'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">संतान संख्या</label>
                                            <input type="number" name="ancestors[<?php echo e($index); ?>][children_count]" value="<?php echo e(old('ancestors.'.$index.'.children_count', $ancestor['children_count'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" min="0" max="50">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">गोत्र</label>
                                            <input type="text" name="ancestors[<?php echo e($index); ?>][gotra]" value="<?php echo e(old('ancestors.'.$index.'.gotra', $ancestor['gotra'] ?? '')); ?>"
                                                   class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endif; ?>
                    </div>
                    <button type="button" id="add-ancestor" class="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-navy-600 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 hover:bg-gray-50 dark:hover:bg-navy-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        पूर्वज जोड़ें
                    </button>
                </div>
            </div>

            <!-- Authentication Section -->
            <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">लॉगिन जानकारी</h3>
                </div>
                <div class="px-6 py-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ईमेल *</label>
                            <input type="email" name="email" id="email" value="<?php echo e(old('email', $member->email)); ?>"
                                   class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" required>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Password Change Section -->
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-navy-700">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-4">पासवर्ड बदलें (वैकल्पिक)</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Current Password -->
                            <div>
                                <label for="current_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">वर्तमान पासवर्ड</label>
                                <input type="password" name="current_password" id="current_password"
                                       class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                <?php $__errorArgs = ['current_password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- New Password -->
                            <div>
                                <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">नया पासवर्ड</label>
                                <input type="password" name="password" id="password"
                                       class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                                <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-400"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Confirm Password -->
                            <div class="md:col-span-2">
                                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">नया पासवर्ड पुष्टि करें</label>
                                <input type="password" name="password_confirmation" id="password_confirmation"
                                       class="mt-1 block w-full px-4 py-3 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                            पासवर्ड बदलने के लिए सभी तीन फील्ड भरना आवश्यक है। न्यूनतम 8 अक्षर होने चाहिए।
                        </p>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end space-x-4">
                <a href="<?php echo e(route('member.profile')); ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-navy-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-navy-700 hover:bg-gray-50 dark:hover:bg-navy-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                    रद्द करें
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    प्रोफाइल अपडेट करें
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let childIndex = <?php echo e($member->children ? count($member->children) : 0); ?>;
    let ancestorIndex = <?php echo e($member->ancestors ? count($member->ancestors) : 0); ?>;

    // File validation functions
    function validateFileType(file, allowedTypes, errorElementId) {
        const errorElement = document.getElementById(errorElementId);

        if (!allowedTypes.includes(file.type)) {
            errorElement.textContent = 'केवल JPG, PNG या JPEG फाइल अपलोड करें।';
            errorElement.classList.remove('hidden');
            return false;
        } else {
            errorElement.classList.add('hidden');
            return true;
        }
    }

    function validateFileSize(file, maxSizeMB, errorElementId) {
        const maxSize = maxSizeMB * 1024 * 1024; // Convert MB to bytes
        const errorElement = document.getElementById(errorElementId);

        if (file.size > maxSize) {
            errorElement.textContent = `फाइल का साइज ${maxSizeMB}MB से कम होना चाहिए। वर्तमान साइज: ${(file.size / (1024 * 1024)).toFixed(2)}MB`;
            errorElement.classList.remove('hidden');
            return false;
        } else {
            errorElement.classList.add('hidden');
            return true;
        }
    }

    function showProgress(type) {
        const progressDiv = document.getElementById(`${type}-progress`);
        const progressBar = document.getElementById(`${type}-progress-bar`);
        const progressText = document.getElementById(`${type}-progress-text`);

        progressDiv.classList.remove('hidden');

        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;

            progressBar.style.width = progress + '%';
            progressText.textContent = Math.round(progress) + '%';

            if (progress >= 90) {
                clearInterval(interval);
                setTimeout(() => {
                    progressBar.style.width = '100%';
                    progressText.textContent = '100%';
                    setTimeout(() => {
                        progressDiv.classList.add('hidden');
                    }, 500);
                }, 200);
            }
        }, 100);
    }

    // Photo file validation
    const photoInput = document.getElementById('photo');
    if (photoInput) {
        photoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                const isValidType = validateFileType(file, allowedTypes, 'photo-error');
                const isValidSize = validateFileSize(file, 2, 'photo-error');

                if (!isValidType || !isValidSize) {
                    e.target.value = ''; // Clear the input
                    return;
                }

                // Show progress loader
                showProgress('photo');
            }
        });
    }

    // Signature file validation
    const signatureInput = document.getElementById('signature');
    if (signatureInput) {
        signatureInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                const isValidType = validateFileType(file, allowedTypes, 'signature-error');
                const isValidSize = validateFileSize(file, 1, 'signature-error');

                if (!isValidType || !isValidSize) {
                    e.target.value = ''; // Clear the input
                    return;
                }

                // Show progress loader
                showProgress('signature');
            }
        });
    }

    // Add child functionality
    document.getElementById('add-child').addEventListener('click', function() {
        const container = document.getElementById('children-container');
        const childEntry = document.createElement('div');
        childEntry.className = 'child-entry border border-gray-200 dark:border-navy-600 rounded-lg p-4 mb-4';
        childEntry.innerHTML = `
            <div class="flex justify-between items-center mb-3">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">बच्चा ${childIndex + 1}</h4>
                <button type="button" class="remove-child text-red-600 hover:text-red-800 text-sm">हटाएं</button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">नाम</label>
                    <input type="text" name="children[${childIndex}][name]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">लिंग</label>
                    <select name="children[${childIndex}][gender]"
                            class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                        <option value="">चुनें</option>
                        <option value="पुत्र">पुत्र</option>
                        <option value="पुत्री">पुत्री</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">जन्म तिथि</label>
                    <input type="date" name="children[${childIndex}][dob]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                </div>
            </div>
        `;
        container.appendChild(childEntry);
        childIndex++;
    });

    // Add ancestor functionality
    document.getElementById('add-ancestor').addEventListener('click', function() {
        const container = document.getElementById('ancestors-container');
        const ancestorEntry = document.createElement('div');
        ancestorEntry.className = 'ancestor-entry border border-gray-200 dark:border-navy-600 rounded-lg p-4 mb-4';
        ancestorEntry.innerHTML = `
            <div class="flex justify-between items-center mb-3">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">पूर्वज ${ancestorIndex + 1}</h4>
                <button type="button" class="remove-ancestor text-red-600 hover:text-red-800 text-sm">हटाएं</button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">नाम *</label>
                    <input type="text" name="ancestors[${ancestorIndex}][name]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">पीढ़ी स्तर *</label>
                    <select name="ancestors[${ancestorIndex}][generation_level]"
                            class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" required>
                        <option value="">चुनें</option>
                        <option value="1">1वीं पीढ़ी (माता-पिता)</option>
                        <option value="2">2वीं पीढ़ी (दादा-दादी)</option>
                        <option value="3">3वीं पीढ़ी</option>
                        <option value="4">4वीं पीढ़ी</option>
                        <option value="5">5वीं पीढ़ी</option>
                        <option value="6">6वीं पीढ़ी</option>
                        <option value="7">7वीं पीढ़ी</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">जन्म तिथि</label>
                    <input type="date" name="ancestors[${ancestorIndex}][birth_date]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">मृत्यु तिथि</label>
                    <input type="date" name="ancestors[${ancestorIndex}][death_date]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">जन्म स्थान</label>
                    <input type="text" name="ancestors[${ancestorIndex}][birth_place]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">व्यवसाय</label>
                    <input type="text" name="ancestors[${ancestorIndex}][occupation]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">पत्नी/पति का नाम</label>
                    <input type="text" name="ancestors[${ancestorIndex}][spouse]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">संतान संख्या</label>
                    <input type="number" name="ancestors[${ancestorIndex}][children_count]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200" min="0" max="50">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">गोत्र</label>
                    <input type="text" name="ancestors[${ancestorIndex}][gotra]"
                           class="mt-1 block w-full px-3 py-2 rounded-lg border-2 border-gray-300 dark:border-navy-600 dark:bg-navy-700 dark:text-white shadow-sm focus:border-teal-500 focus:ring-teal-500 text-base transition-colors duration-200">
                </div>
            </div>
        `;
        container.appendChild(ancestorEntry);
        ancestorIndex++;
    });

    // Remove child and ancestor functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-child')) {
            e.target.closest('.child-entry').remove();
        }
        if (e.target.classList.contains('remove-ancestor')) {
            e.target.closest('.ancestor-entry').remove();
        }
    });
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.member', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web-yadav-samaj\resources\views/member/profile-edit.blade.php ENDPATH**/ ?>