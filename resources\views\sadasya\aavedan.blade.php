@extends('layouts.app')

@section('title', 'सदस्यता पंजीयन')

@section('content')
    <div class="bg-gradient-to-b to-gray-50 min-h-screen">
        <div class="container-custom py-16">
            <!-- Page Header -->
            <div class="text-center mb-12">
                <div class="inline-block mx-auto mb-3">
                    <div class="w-10 h-1 bg-saffron-500 rounded-full mb-1 mx-auto"></div>
                    <div class="w-20 h-1 bg-saffron-500 rounded-full opacity-60 mx-auto"></div>
                </div>
                <h1 class="text-3xl md:text-4xl font-bold text-navy-900 mb-4">सदस्यता पंजीयन</h1>
                <p class="text-gray-600 max-w-2xl mx-auto">कृपया निम्नलिखित फॉर्म में सभी आवश्यक जानकारी भरें</p>
            </div>

            <!-- Application Form -->
            <div class="max-w-7xl mx-auto p-3">

                <!-- Organization Header -->
                <div class="bg-white rounded shadow-lg mb-8 overflow-hidden">
                    <div class="bg-gradient-to-r from-teal-600 to-teal-700 text-white p-6">
                        <div class="flex flex-col md:flex-row items-center justify-between">
                            <div class="text-center md:text-left mb-4 md:mb-0">
                                <h2 class="text-xl md:text-2xl font-bold mb-2">छत्तीसगढ़ यादव शासकीय सेवक समिति</h2>
                                <p class="text-teal-100">पं. क्रमांक. 122202411281 दिनांक 23.10.2024</p>
                                <p class="text-teal-100">पता : A-26, गायत्री नगर, रायपुर</p>
                            </div>
                            <div class="flex flex-col items-center space-y-2">
                                <div class="bg-white text-teal-700 px-3 py-1 rounded text-sm font-semibold">
                                    संरक्षक सदस्यता 10,000 रुपए एकमुस्त
                                </div>
                                <div class="bg-white text-teal-700 px-3 py-1 rounded text-sm font-semibold">
                                    आजीवन सदस्यता 5,000 रुपए एकमुस्त
                                </div>
                                <div class="bg-white text-teal-700 px-3 py-1 rounded text-sm font-semibold">
                                    वार्षिक सदस्यता 1200 रुपए प्रति वर्ष
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <form action="{{ route('sadasya.aavedan.submit') }}" method="POST" enctype="multipart/form-data"
                    class="space-y-6">
                    @csrf

                    {{-- Display validation errors --}}
                    @if ($errors->any())
                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">कृपया निम्नलिखित त्रुटियों को ठीक करें:</h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <ul class="list-disc list-inside space-y-1">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <!-- Photo Upload -->
                        <div>
                            <label class="block text-gray-700 font-semibold mb-4">
                                पासपोर्ट साइज फोटो
                            </label>
                            <div
                                class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-teal-500 transition duration-200 @error('photo') border-red-500 @enderror">
                                <div id="photo-preview" class="mb-4">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none"
                                        viewBox="0 0 48 48">
                                        <path
                                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-8l-6-6-6 6m6-6v14" />
                                    </svg>
                                    <img id="photo-image"
                                        class="hidden mx-auto max-h-32 max-w-[120px] w-auto object-contain" src=""
                                        alt="Photo preview">
                                </div>
                                <input type="file" id="photo" name="photo" accept="image/jpeg,image/png,image/jpg" class="hidden">
                                <label for="photo"
                                    class="cursor-pointer bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition duration-200 inline-block">
                                    फोटो चुनें
                                </label>
                                <div id="photo-progress" class="hidden mt-2">
                                    <div class="bg-gray-200 rounded-full h-2">
                                        <div id="photo-progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">अपलोड हो रहा है... <span id="photo-progress-text">0%</span></p>
                                </div>
                                <div id="photo-error" class="text-red-500 text-sm mt-2 hidden"></div>
                                @error('photo')
                                    <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                                @else
                                    <p class="text-sm text-gray-500 mt-2">JPG या PNG (अधिकतम 2MB)</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Signature Upload -->
                        <div>
                            <label class="block text-gray-700 font-semibold mb-4">
                                हस्ताक्षर
                            </label>
                            <div
                                class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-teal-500 transition duration-200 @error('signature') border-red-500 @enderror">
                                <div id="signature-preview" class="mb-4">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none"
                                        viewBox="0 0 48 48">
                                        <path
                                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-8l-6-6-6 6m6-6v14" />
                                    </svg>
                                    <img id="signature-image" class="hidden mx-auto w-24 h-24 object-cover rounded" src=""
                                        alt="Signature preview">
                                </div>
                                <input type="file" id="signature" name="signature" accept="image/jpeg,image/png,image/jpg" class="hidden">
                                <label for="signature"
                                    class="cursor-pointer bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 transition duration-200 inline-block">
                                    हस्ताक्षर चुनें
                                </label>
                                <div id="signature-progress" class="hidden mt-2">
                                    <div class="bg-gray-200 rounded-full h-2">
                                        <div id="signature-progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">अपलोड हो रहा है... <span id="signature-progress-text">0%</span></p>
                                </div>
                                <div id="signature-error" class="text-red-500 text-sm mt-2 hidden"></div>
                                @error('signature')
                                    <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                                @else
                                    <p class="text-sm text-gray-500 mt-2">JPG या PNG (अधिकतम 1MB)</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <script>
                        // File validation functions
                        function validateFileType(file, allowedTypes, errorElementId) {
                            const errorElement = document.getElementById(errorElementId);

                            if (!allowedTypes.includes(file.type)) {
                                errorElement.textContent = 'केवल JPG, PNG या JPEG फाइल अपलोड करें।';
                                errorElement.classList.remove('hidden');
                                return false;
                            } else {
                                errorElement.classList.add('hidden');
                                return true;
                            }
                        }

                        function validateFileSize(file, maxSizeMB, errorElementId) {
                            const maxSize = maxSizeMB * 1024 * 1024; // Convert MB to bytes
                            const errorElement = document.getElementById(errorElementId);

                            if (file.size > maxSize) {
                                errorElement.textContent = `फाइल का साइज ${maxSizeMB}MB से कम होना चाहिए। वर्तमान साइज: ${(file.size / (1024 * 1024)).toFixed(2)}MB`;
                                errorElement.classList.remove('hidden');
                                return false;
                            } else {
                                errorElement.classList.add('hidden');
                                return true;
                            }
                        }

                        function showProgress(type) {
                            const progressDiv = document.getElementById(`${type}-progress`);
                            const progressBar = document.getElementById(`${type}-progress-bar`);
                            const progressText = document.getElementById(`${type}-progress-text`);

                            progressDiv.classList.remove('hidden');

                            let progress = 0;
                            const interval = setInterval(() => {
                                progress += Math.random() * 15;
                                if (progress > 90) progress = 90;

                                progressBar.style.width = progress + '%';
                                progressText.textContent = Math.round(progress) + '%';

                                if (progress >= 90) {
                                    clearInterval(interval);
                                    setTimeout(() => {
                                        progressBar.style.width = '100%';
                                        progressText.textContent = '100%';
                                        setTimeout(() => {
                                            progressDiv.classList.add('hidden');
                                        }, 500);
                                    }, 200);
                                }
                            }, 100);
                        }

                        function previewImage(type) {
                            const input = document.getElementById(type);
                            const preview = document.getElementById(`${type}-image`);
                            const svg = preview.previousElementSibling;
                            const file = input.files[0];

                            if (file) {
                                // Validate file type and size
                                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
                                const maxSize = type === 'photo' ? 2 : 1; // 2MB for photo, 1MB for signature

                                const isValidType = validateFileType(file, allowedTypes, `${type}-error`);
                                const isValidSize = validateFileSize(file, maxSize, `${type}-error`);

                                if (!isValidType || !isValidSize) {
                                    input.value = ''; // Clear the input
                                    return;
                                }

                                // Show progress loader
                                showProgress(type);

                                const reader = new FileReader();
                                reader.onload = function (e) {
                                    preview.src = e.target.result;
                                    preview.classList.remove('hidden');
                                    svg.classList.add('hidden');
                                }
                                reader.readAsDataURL(file);
                            } else {
                                preview.classList.add('hidden');
                                svg.classList.remove('hidden');
                            }
                        }
                    </script>
                    <!-- Personal Information -->
                    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                            <!-- Naam -->
                            <div>
                                <label for="name" class="block text-gray-700 font-semibold mb-2">
                                    नाम <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="name" name="name" required value="{{ old('name') }}"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('name') border-red-500 @enderror"
                                    placeholder="अपना पूरा नाम लिखें">
                                @error('name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Pita ka Naam -->
                            <div>
                                <label for="fathers_husband_name" class="block text-gray-700 font-semibold mb-2">
                                    पिता / पति का नाम
                                </label>
                                <input type="text" id="fathers_husband_name" name="fathers_husband_name" value="{{ old('fathers_husband_name') }}"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('fathers_husband_name') border-red-500 @enderror"
                                    placeholder="पिता / पति का नाम लिखें">
                                @error('fathers_husband_name')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                         

                            <!-- Mobile Number -->
                            <div>
                                <label for="mobile_number" class="block text-gray-700 font-semibold mb-2">
                                    मोबाइल नंबर <span class="text-red-500">*</span>
                                </label>
                                <input type="tel" id="mobile_number" name="mobile_number" required pattern="[0-9]{10}" maxlength="10" value="{{ old('mobile_number') }}"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('mobile_number') border-red-500 @enderror"
                                    placeholder="10 अंकों का मोबाइल नंबर">
                                @error('mobile_number')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @else
                                    <p class="text-xs text-gray-500 mt-1">10 अंकों का मोबाइल नंबर दर्ज करें</p>
                                @enderror
                            </div>

                            <!-- Janma Tithi -->
                            <div>
                                <label for="birth_date" class="block text-gray-700 font-semibold mb-2">
                                    जन्म तिथि <span class="text-red-500">*</span>
                                </label>
                                <input type="date" id="birth_date" name="birth_date" required value="{{ old('birth_date') }}"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('birth_date') border-red-500 @enderror">
                                @error('birth_date')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Email Address -->
                            <div>
                                <label for="email" class="block text-gray-700 font-semibold mb-2">
                                    ईमेल पता <span class="text-red-500">*</span>
                                </label>
                                <input type="email" id="email" name="email" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('email') border-red-500 @enderror"
                                    placeholder="<EMAIL>" value="{{ old('email') }}">
                                @error('email')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @else
                                    <p class="text-sm text-gray-500 mt-1">यह ईमेल सदस्य डैशबोर्ड में लॉगिन के लिए उपयोग होगा</p>
                                @enderror
                            </div>

                            <!-- Login Password -->
                            <div>
                                <label for="password" class="block text-gray-700 font-semibold mb-2">
                                    लॉगिन पासवर्ड <span class="text-red-500">*</span>
                                </label>
                                <input type="password" id="password" name="password" required minlength="8"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('password') border-red-500 @enderror"
                                    placeholder="कम से कम 8 अक्षर का पासवर्ड">
                                @error('password')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @else
                                    <p class="text-sm text-gray-500 mt-1">यह पासवर्ड सदस्य डैशबोर्ड में लॉगिन के लिए उपयोग होगा</p>
                                @enderror
                            </div>

                            <!-- Confirm Password -->
                            <div>
                                <label for="password_confirmation" class="block text-gray-700 font-semibold mb-2">
                                    पासवर्ड पुष्टि <span class="text-red-500">*</span>
                                </label>
                                <input type="password" id="password_confirmation" name="password_confirmation" required minlength="8"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('password_confirmation') border-red-500 @enderror"
                                    placeholder="पासवर्ड दोबारा लिखें">
                                @error('password_confirmation')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Vaivahik Sthiti -->
                            <div>
                                <label for="marital_status" class="block text-gray-700 font-semibold mb-2">
                                    वैवाहिक स्थिति
                                </label>
                                <select id="marital_status" name="marital_status"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('marital_status') border-red-500 @enderror">
                                    <option value="">चुनें</option>
                                    <option value="अविवाहित" {{ old('marital_status') == 'अविवाहित' ? 'selected' : '' }}>अविवाहित</option>
                                    <option value="विवाहित" {{ old('marital_status') == 'विवाहित' ? 'selected' : '' }}>विवाहित</option>
                                    <option value="विधुर/विधवा" {{ old('marital_status') == 'विधुर/विधवा' ? 'selected' : '' }}>विधुर/विधवा</option>
                                </select>
                                @error('marital_status')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Vivah Tithi (if married) -->
                            <div id="vivah_tithi_field" style="display: none;">
                                <label for="vivah_tithi" class="block text-gray-700 font-semibold mb-2">
                                    विवाह तिथि
                                </label>
                                <input type="date" id="vivah_tithi" name="vivah_tithi" value="{{ old('vivah_tithi') }}"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('vivah_tithi') border-red-500 @enderror">
                                @error('vivah_tithi')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Spouse Details (if married) -->
                            <div id="spouse_details_fields" style="display: none;" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- Spouse Name -->
                                <div>
                                    <label for="spouse_name" class="block text-gray-700 font-semibold mb-2">
                                        पति/पत्नी का नाम
                                    </label>
                                    <input type="text" id="spouse_name" name="spouse_name" value="{{ old('spouse_name') }}"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('spouse_name') border-red-500 @enderror"
                                        placeholder="पति/पत्नी का पूरा नाम">
                                    @error('spouse_name')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Spouse Education -->
                                <div>
                                    <label for="spouse_education" class="block text-gray-700 font-semibold mb-2">
                                        पति/पत्नी की शैक्षणिक योग्यता
                                    </label>
                                    <input type="text" id="spouse_education" name="spouse_education" value="{{ old('spouse_education') }}"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('spouse_education') border-red-500 @enderror"
                                        placeholder="जैसे: स्नातक, परास्नातक, डिप्लोमा आदि">
                                    @error('spouse_education')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Spouse Education Details -->
                                <div class="md:col-span-2">
                                    <label for="spouse_education_details" class="block text-gray-700 font-semibold mb-2">
                                        पति/पत्नी का शैक्षणिक विवरण
                                    </label>
                                    <textarea id="spouse_education_details" name="spouse_education_details" rows="3"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('spouse_education_details') border-red-500 @enderror"
                                        placeholder="शैक्षणिक संस्थान, विषय, वर्ष आदि का विवरण">{{ old('spouse_education_details') }}</textarea>
                                    @error('spouse_education_details')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Spouse Work Details -->
                                <div class="md:col-span-2">
                                    <label for="spouse_work_details" class="block text-gray-700 font-semibold mb-2">
                                        पति/पत्नी का कार्य विवरण
                                    </label>
                                    <textarea id="spouse_work_details" name="spouse_work_details" rows="3"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('spouse_work_details') border-red-500 @enderror"
                                        placeholder="वर्तमान कार्य, पद, संस्थान आदि का विवरण">{{ old('spouse_work_details') }}</textarea>
                                    @error('spouse_work_details')
                                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Parivar me Sadasya ki Sankhya -->
                            <div>
                                <label for="family_members" class="block text-gray-700 font-semibold mb-2">
                                    परिवार में सदस्यों की संख्या
                                </label>
                                <input type="number" id="family_members" name="family_members" min="1" value="{{ old('family_members') }}"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('family_members') border-red-500 @enderror"
                                    placeholder="सदस्यों की संख्या">
                                @error('family_members')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Shaikshanik Yogyata -->
                            <div>
                                <label for="education_qualification_id" class="block text-gray-700 font-semibold mb-2">
                                    शैक्षणिक योग्यता
                                </label>
                                <select id="education_qualification_id" name="education_qualification_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200">
                                    <option value="">चुनें</option>
                                    @foreach($educationQualifications as $qualification)
                                        <option value="{{ $qualification->id }}" {{ old('education_qualification_id') == $qualification->id ? 'selected' : '' }}>
                                            {{ $qualification->name }}
                                        </option>
                                    @endforeach
                                </select>

                                <!-- Keep the old text field for backward compatibility -->
                                <input type="hidden" id="education" name="education" value="">

                                <script>
                                    document.getElementById('education_qualification_id').addEventListener('change', function() {
                                        const selectedOption = this.options[this.selectedIndex];
                                        document.getElementById('education').value = selectedOption.text;
                                    });
                                </script>
                            </div>

                            <!-- Course or Stream Name -->
                            <div>
                                <label for="course_stream_name" class="block text-gray-700 font-semibold mb-2">
                                    कोर्स या स्ट्रीम का नाम
                                </label>
                                <input type="text" id="course_stream_name" name="course_stream_name" value="{{ old('course_stream_name') }}"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                    placeholder="कोर्स या स्ट्रीम का नाम लिखें">
                            </div>

                            <!-- Jati/Upjati/Gotra -->
                            <div>
                                <label for="caste_details" class="block text-gray-700 font-semibold mb-2">
                                    जाति/उपजाति/गोत्र
                                </label>
                                <input type="text" id="caste_details" name="caste_details"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                    placeholder="जाति/उपजाति/गोत्र लिखें">
                            </div>

                            <!-- Yadav Varg -->
                            <div>
                                <label for="yadav_varg_id" class="block text-gray-700 font-semibold mb-2">
                                    यादव वर्ग
                                </label>
                                <select id="yadav_varg_id" name="yadav_varg_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200">
                                    <option value="">चुनें</option>
                                    @foreach($yadavVargs as $yadavVarg)
                                        <option value="{{ $yadavVarg->id }}" {{ old('yadav_varg_id') == $yadavVarg->id ? 'selected' : '' }}>
                                            {{ $yadavVarg->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Vibhagiy Padnaam -->
                            <div>
                                <label for="vibhagiy_padnaam" class="block text-gray-700 font-semibold mb-2">
                                    विभागीय पदनाम
                                </label>
                                <input type="text" id="vibhagiy_padnaam" name="vibhagiy_padnaam" value="{{ old('vibhagiy_padnaam') }}"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                    placeholder="विभागीय पदनाम लिखें">
                            </div>

                            <!-- Vibhag ka Naam -->
                            <div>
                                <label for="department_master_id" class="block text-gray-700 font-semibold mb-2">
                                    विभाग का नाम
                                </label>
                                <select id="department_master_id" name="department_master_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200">
                                    <option value="">चुनें</option>
                                    @foreach($departmentMasters as $department)
                                        <option value="{{ $department->id }}" {{ old('department_master_id') == $department->id ? 'selected' : '' }}>
                                            {{ $department->name }}
                                        </option>
                                    @endforeach
                                </select>

                                <!-- Keep the old text field for backward compatibility -->
                                <input type="hidden" id="department_name" name="department_name" value="">

                                <script>
                                    document.getElementById('department_master_id').addEventListener('change', function() {
                                        const selectedOption = this.options[this.selectedIndex];
                                        document.getElementById('department_name').value = selectedOption.text;
                                    });
                                </script>
                            </div>

                            <!-- Karyalay -->
                            <div>
                                <label for="office_master_id" class="block text-gray-700 font-semibold mb-2">
                                    कार्यालय प्रकार
                                </label>
                                <select id="office_master_id" name="office_master_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200">
                                    <option value="">चुनें</option>
                                    @foreach($officeMasters as $office)
                                        <option value="{{ $office->id }}" {{ old('office_master_id') == $office->id ? 'selected' : '' }}>
                                            {{ $office->name }}
                                        </option>
                                    @endforeach
                                </select>

                                <!-- Keep the old text field for backward compatibility -->
                                <input type="hidden" id="office" name="office" value="">

                                <script>
                                    document.getElementById('office_master_id').addEventListener('change', function() {
                                        const selectedOption = this.options[this.selectedIndex];
                                        document.getElementById('office').value = selectedOption.text;
                                    });
                                </script>
                            </div>

                               <!-- Karyalay ka Pata -->
                            <div>
                                <label for="karyalay_ka_pata" class="block text-gray-700 font-semibold mb-2">
                                    कार्यालय का पता
                                </label>
                                <textarea id="karyalay_ka_pata" name="karyalay_ka_pata" rows="3"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                    placeholder="कार्यालय का पूरा पता लिखें">{{ old('karyalay_ka_pata') }}</textarea>
                            </div>

                            <!-- Vartaman Pata -->
                            <div>
                                <label for="vartaman_pata" class="block text-gray-700 font-semibold mb-2">
                                    वर्तमान पता
                                </label>
                                <textarea id="vartaman_pata" name="vartaman_pata" rows="3"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                    placeholder="वर्तमान पूरा पता लिखें">{{ old('vartaman_pata') }}</textarea>
                            </div>

                            <!-- Isthayi Pata -->
                            <div>
                                <label for="isthayi_pata" class="block text-gray-700 font-semibold mb-2">
                                    स्थायी पता
                                </label>
                                <textarea id="isthayi_pata" name="isthayi_pata" rows="3"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                    placeholder="स्थायी पूरा पता लिखें">{{ old('isthayi_pata') }}</textarea>
                            </div>

                            <!-- Keep old address field for backward compatibility -->
                            <input type="hidden" id="address" name="address" value="">
                        </div>
                    </div>

                    <!-- Location Information -->
                    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-6">स्थान की जानकारी</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <!-- Division -->
                            <div>
                                <label for="division_master_id" class="block text-gray-700 font-semibold mb-2">
                                    संभाग <span class="text-red-500">*</span>
                                </label>
                                <select id="division_master_id" name="division_master_id" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('division_master_id') border-red-500 @enderror">
                                    <option value="">संभाग चुनें</option>
                                    @foreach($divisionMasters as $division)
                                        <option value="{{ $division->id }}" data-division-code="{{ $division->division_code }}" {{ old('division_master_id') == $division->id ? 'selected' : '' }}>
                                            {{ $division->division_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('division_master_id')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- District -->
                            <div>
                                <label for="district_master_id" class="block text-gray-700 font-semibold mb-2">
                                    जिला <span class="text-red-500">*</span>
                                </label>
                                <select id="district_master_id" name="district_master_id" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('district_master_id') border-red-500 @enderror">
                                    <option value="">जिला चुनें</option>
                                    @foreach($districtMasters as $district)
                                        <option value="{{ $district->id }}" data-division-code="{{ $district->division_code }}" data-district-lgd="{{ $district->district_lgd_code }}" {{ old('district_master_id') == $district->id ? 'selected' : '' }}>
                                            {{ $district->district_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('district_master_id')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Vikaskhand -->
                            <div>
                                <label for="vikaskhand_master_id" class="block text-gray-700 font-semibold mb-2">
                                    विकासखंड <span class="text-red-500">*</span>
                                </label>
                                <select id="vikaskhand_master_id" name="vikaskhand_master_id" required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200 @error('vikaskhand_master_id') border-red-500 @enderror">
                                    <option value="">विकासखंड चुनें</option>
                                    @foreach($vikaskhandMasters as $vikaskhand)
                                        <option value="{{ $vikaskhand->id }}" data-district-lgd="{{ $vikaskhand->district_lgd_code }}" {{ old('vikaskhand_master_id') == $vikaskhand->id ? 'selected' : '' }}>
                                            {{ $vikaskhand->sub_district_name_eng }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('vikaskhand_master_id')
                                    <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-6">पुत्र पुत्री विवरण</h3>
                        <div class="grid grid-cols-1 md:grid-cols-1 gap-4 mb-6">
                            <!-- Family Details -->
                            <div id="children-details" class="space-y-6">
                                <div class="child-entry border border-gray-200 rounded-lg p-4">
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <!-- Name -->
                                        <div>
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                नाम (पुत्र/पुत्री)
                                            </label>
                                            <input type="text" name="child_name[]"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                                placeholder="पुत्र/पुत्री का नाम">
                                        </div>

                                        <!-- Gender -->
                                        <div>
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                लिंग (पुत्र/पुत्री)
                                            </label>
                                            <select name="child_gender[]"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200">
                                                <option value="">-- चयन करें --</option>
                                                <option value="पुत्र">पुत्र</option>
                                                <option value="पुत्री">पुत्री</option>
                                            </select>
                                        </div>

                                        <!-- Date of Birth -->
                                        <div>
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                जन्म तिथि
                                            </label>
                                            <input type="date" name="child_dob[]"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200">
                                        </div>

                                        <!-- Shaikshanik Yogyata -->
                                        <div>
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                शैक्षणिक योग्यता
                                            </label>
                                            <input type="text" name="child_education[]"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                                placeholder="शैक्षणिक योग्यता">
                                        </div>

                                        <!-- Shaikshanik Vivran -->
                                        <div class="lg:col-span-3">
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                शैक्षणिक विवरण
                                            </label>
                                            <textarea name="child_education_details[]" rows="3"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                                placeholder="शैक्षणिक संस्थान, विषय, वर्ष आदि का विवरण"></textarea>
                                        </div>

                                        <!-- Upjivika -->
                                        <div>
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                उपजीविका
                                            </label>
                                            <select name="child_occupation[]"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200">
                                                <option value="">-- चयन करें --</option>
                                                <option value="शासकीय नौकरी">शासकीय नौकरी</option>
                                                <option value="अर्ध शासकीय नौकरी">अर्ध शासकीय नौकरी</option>
                                                <option value="निजी क्षेत्र">निजी क्षेत्र</option>
                                                <option value="बेरोजगार">बेरोजगार</option>
                                                <option value="अध्ययनरत">अध्ययनरत</option>
                                            </select>
                                        </div>

                                        <!-- Vaivahik Sthiti -->
                                        <div>
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                वैवाहिक स्थिति
                                            </label>
                                            <select name="child_marital_status[]" onchange="toggleSpouseFields(this)"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200">
                                                <option value="">-- चयन करें --</option>
                                                <option value="अविवाहित">अविवाहित</option>
                                                <option value="विवाहित">विवाहित</option>
                                                <option value="विधुर/विधवा">विधुर/विधवा</option>
                                            </select>
                                        </div>

                                        <!-- Pati/Patni ka Naam (if married) -->
                                        <div class="spouse-fields" style="display: none;">
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                पति/पत्नी का नाम
                                            </label>
                                            <input type="text" name="child_spouse_name[]"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                                placeholder="पति/पत्नी का नाम">
                                        </div>

                                        <!-- Pati/Patni ki Aajivika (if married) -->
                                        <div class="spouse-fields" style="display: none;">
                                            <label class="block text-gray-700 font-semibold mb-2">
                                                पति/पत्नी की आजीविका
                                            </label>
                                            <input type="text" name="child_spouse_occupation[]"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent transition duration-200"
                                                placeholder="पति/पत्नी की आजीविका">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Add More Button -->
                            <button type="button" onclick="addChildEntry()"
                                class="mt-4 px-4 py-2 bg-teal-600 text-white rounded hover:bg-teal-700">
                                + और जोड़ें
                            </button>

                            <script>
                                function addChildEntry() {
                                    const container = document.getElementById('children-details');
                                    const newEntry = document.querySelector('.child-entry').cloneNode(true);
                                    // Clear all inputs and selects in the new cloned entry
                                    newEntry.querySelectorAll('input').forEach(input => input.value = '');
                                    newEntry.querySelectorAll('select').forEach(select => select.selectedIndex = 0);
                                    // Hide spouse fields in new entry
                                    newEntry.querySelectorAll('.spouse-fields').forEach(field => field.style.display = 'none');
                                    container.appendChild(newEntry);
                                }

                                function toggleSpouseFields(selectElement) {
                                    const childEntry = selectElement.closest('.child-entry');
                                    const spouseFields = childEntry.querySelectorAll('.spouse-fields');

                                    if (selectElement.value === 'विवाहित') {
                                        spouseFields.forEach(field => field.style.display = 'block');
                                    } else {
                                        spouseFields.forEach(field => field.style.display = 'none');
                                        // Clear spouse field values when hidden
                                        spouseFields.forEach(field => {
                                            const inputs = field.querySelectorAll('input');
                                            inputs.forEach(input => input.value = '');
                                        });
                                    }
                                }
                            </script>

                        </div>
                    </div>

                    <!-- Membership Type and Fees -->
                    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-6">सदस्यता का प्रकार</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                            @foreach($membershipVargs as $varg)
                                <div class="border border-gray-300 rounded-lg p-4 hover:border-teal-500 transition duration-200">
                                    <label class="flex items-center space-x-3 cursor-pointer">
                                        <input type="radio" name="membership_varg_id" value="{{ $varg->id }}" required
                                            class="h-4 w-4 text-teal-600 focus:ring-teal-500"
                                            {{ old('membership_varg_id') == $varg->id ? 'checked' : '' }}>
                                        <div>
                                            <div class="font-semibold text-gray-800">{{ $varg->name }}</div>
                                            @if($varg->fee)
                                                <div class="text-teal-600 font-bold">₹{{ number_format($varg->fee, 0) }}</div>
                                            @endif
                                            @if($varg->description)
                                                <div class="text-sm text-gray-600">{{ $varg->description }}</div>
                                            @endif
                                        </div>
                                    </label>
                                </div>
                            @endforeach
                        </div>

                        <!-- Keep the old field for backward compatibility -->
                        <input type="hidden" id="membership_type" name="membership_type" value="">

                        <script>
                            document.querySelectorAll('input[name="membership_varg_id"]').forEach(function(radio) {
                                radio.addEventListener('change', function() {
                                    if (this.checked) {
                                        const label = this.closest('label').querySelector('.font-semibold').textContent;
                                        document.getElementById('membership_type').value = label;
                                    }
                                });
                            });
                        </script>

                        @error('membership_varg_id')
                            <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                        @enderror
                        @error('membership_type')
                            <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                        @enderror
                    </div>
                    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                        <!-- Seven Generation Input Section -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-4">
                                <label class="block text-gray-700 font-semibold">
                                    7 पिढ़ी की संपूर्ण जानकारी
                                </label>
                                <div class="text-sm text-gray-600">
                                    कुल पिढ़ियां: <span id="generationCount" class="font-semibold text-teal-600">0</span>/7
                                </div>
                            </div>

                            <div id="generationWrapper" class="space-y-6">
                                <!-- Generations will be added here dynamically -->
                            </div>

                            <!-- Add More Button -->
                            <div class="mt-6 text-center">
                                <button type="button" id="addGenerationBtn"
                                    class="px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition duration-200 shadow-md disabled:bg-gray-400 disabled:cursor-not-allowed">
                                    <span class="mr-2">+</span> नई पिढ़ी जोड़ें
                                </button>
                            </div>
                        </div>
                    </div>

                    <script>
                        let generationCounter = 0;
                        const maxGenerations = 7;

                        const generationLabels = [
                            'पिता (1st पिढ़ी)',
                            'दादा (2nd पिढ़ी)',
                            'परदादा (3rd पिढ़ी)',
                            'परदादाजी के पिता (4th पिढ़ी)',
                            'परदादाजी के दादा (5th पिढ़ी)',
                            'परदादाजी के परदादा (6th पिढ़ी)',
                            'सबसे पुराने पूर्वज (7th पिढ़ी)'
                        ];

                        const generationColors = [
                            'border-green-400 bg-green-50',
                            'border-blue-400 bg-blue-50',
                            'border-purple-400 bg-purple-50',
                            'border-yellow-400 bg-yellow-50',
                            'border-red-400 bg-red-50',
                            'border-indigo-400 bg-indigo-50',
                            'border-pink-400 bg-pink-50'
                        ];

                        function createGenerationCard(level) {
                            const generationCard = document.createElement('div');
                            generationCard.className = `generation-card border-l-4 ${generationColors[level - 1]} p-6 rounded-lg shadow-sm`;
                            generationCard.innerHTML = `
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800">${generationLabels[level - 1]}</h3>
                <button type="button" class="remove-btn text-red-500 hover:text-red-700 font-bold text-xl px-2 py-1 hover:bg-red-100 rounded transition duration-200" title="इस पिढ़ी को हटाएं">
                    ×
                </button>
            </div>

            <input type="hidden" name="generation_level[]" value="${level}">

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Basic Information -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">पूरा नाम *</label>
                    <input type="text" name="ancestor_name_${level}[]" placeholder="पूर्वज का पूरा नाम" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500" required>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">जन्म तिथि (अनुमानित)</label>
                    <input type="text" name="ancestor_birth_date_${level}[]" placeholder="वर्ष या दिनांक" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">मृत्यु तिथि (यदि ज्ञात हो)</label>
                    <input type="text" name="ancestor_death_date_${level}[]" placeholder="वर्ष या दिनांक" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">जन्म स्थान</label>
                    <input type="text" name="ancestor_birth_place_${level}[]" placeholder="गांव/शहर" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">व्यवसाय/पेशा</label>
                    <input type="text" name="ancestor_occupation_${level}[]" placeholder="व्यवसाय या पेशा" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">पत्नी का नाम</label>
                    <input type="text" name="ancestor_spouse_${level}[]" placeholder="पत्नी का नाम" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">संतान संख्या</label>
                    <input type="number" name="ancestor_children_count_${level}[]" placeholder="कुल संतान" min="0"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                </div>

                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">संतान विवरण</label>
                    <textarea name="ancestor_children_details_${level}[]" rows="3" placeholder="संतान का नाम, उम्र, व्यवसाय आदि का विवरण"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500"></textarea>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">गोत्र/कुल</label>
                    <input type="text" name="ancestor_gotra_${level}[]" placeholder="गोत्र या कुल"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500">
                </div>
            </div>
        `;

                            return generationCard;
                        }

                        function addGeneration() {
                            if (generationCounter >= maxGenerations) {
                                alert(`अधिकतम ${maxGenerations} पिढ़ियां ही जोड़ी जा सकती हैं।`);
                                return;
                            }

                            generationCounter++;
                            const generationCard = createGenerationCard(generationCounter);
                            generationCard.classList.add('fade-in');

                            document.getElementById('generationWrapper').appendChild(generationCard);
                            updateGenerationCount();
                            updateButtonState();

                            // Add remove functionality
                            generationCard.querySelector('.remove-btn').addEventListener('click', function () {
                                removeGeneration(generationCard);
                            });
                        }

                        function removeGeneration(cardElement) {
                            if (confirm('क्या आप वाकई इस पिढ़ी की जानकारी हटाना चाहते हैं?')) {
                                cardElement.remove();
                                renumberGenerations();
                                updateGenerationCount();
                                updateButtonState();
                            }
                        }

                        function renumberGenerations() {
                            const cards = document.querySelectorAll('.generation-card');
                            generationCounter = 0;

                            cards.forEach((card, index) => {
                                generationCounter = index + 1;
                                const level = generationCounter;

                                // Update hidden input
                                card.querySelector('input[name="generation_level[]"]').value = level;

                                // Update header
                                card.querySelector('h3').textContent = generationLabels[level - 1];

                                // Update color scheme
                                card.className =
                                    `generation-card border-l-4 ${generationColors[level - 1]} p-6 rounded-lg shadow-sm fade-in`;

                                // Update all field names
                                const inputs = card.querySelectorAll('input, textarea');
                                inputs.forEach(input => {
                                    const name = input.getAttribute('name');
                                    if (name && name.includes('_')) {
                                        const parts = name.split('_');
                                        if (parts.length >= 3 && !isNaN(parts[2].replace('[]', ''))) {
                                            parts[2] = level + (name.endsWith('[]') ? '[]' : '');
                                            input.setAttribute('name', parts.join('_'));
                                        }
                                    }
                                });
                            });
                        }

                        function updateGenerationCount() {
                            document.getElementById('generationCount').textContent = generationCounter;
                        }

                        function updateButtonState() {
                            const addBtn = document.getElementById('addGenerationBtn');
                            if (generationCounter >= maxGenerations) {
                                addBtn.disabled = true;
                                addBtn.innerHTML = '<span class="mr-2">✓</span> सभी 7 पिढ़ियां पूर्ण';
                            } else {
                                addBtn.disabled = false;
                                addBtn.innerHTML = '<span class="mr-2">+</span> नई पिढ़ी जोड़ें';
                            }
                        }

                        // Event Listeners
                        document.getElementById('addGenerationBtn').addEventListener('click', addGeneration);

                        // CSS for animations
                        const style = document.createElement('style');
                        style.textContent = `
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .generation-card {
            transition: all 0.3s ease;
        }

        .generation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
    `;
                        document.head.appendChild(style);

                        // Initialize
                        updateGenerationCount();
                        updateButtonState();
                    </script>
                    <!-- Signature Section -->
                    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Left Column -->
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-4">प्रस्तावक सदस्य</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label for="member_name_signature" class="block text-gray-700 mb-2">सदस्य का
                                            नाम <span class="text-red-500">*</span></label>
                                        <input type="text" id="member_name_signature" name="member_name_signature" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent @error('member_name_signature') border-red-500 @enderror"
                                            placeholder="नाम लिखें" value="{{ old('member_name_signature') }}">
                                        @error('member_name_signature')
                                            <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="vibhag" class="block text-gray-700 mb-2">विभाग</label>
                                        <select name="vibhag" id="vibhag"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent">
                                            <option value="">विभाग चुनें</option>
                                            <option value="police">पुलिस विभाग</option>
                                            <option value="shiksha">शिक्षा विभाग</option>
                                            <option value="rajasva">राजस्व विभाग</option>
                                            <option value="swasthya">स्वास्थ्य विभाग</option>
                                            <option value="krishi">कृषि विभाग</option>
                                            <option value="van">वन विभाग</option>
                                            <option value="other">अन्य</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div>
                                <h4 class="font-semibold text-gray-800 mb-4">संपर्क विवरण</h4>
                                <div class="space-y-4">
                                    <div>
                                        <label for="mobile" class="block text-gray-700 mb-2">मोबाइल नंबर</label>
                                        <input type="tel" id="mobile" name="mobile"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                                        placeholder="मोबाइल नंबर लिखें"
                                        pattern="^[6-9][0-9]{9}$"
                                        maxlength="10"
                                        title="10 अंकों का मोबाइल नंबर दर्ज करें जो 6, 7, 8, या 9 से शुरू होता हो"
                                        required>

                                    </div>

                                    <div>
                                        <label for="proposer_address" class="block text-gray-700 mb-2">पता</label>
                                        <textarea id="proposer_address" name="proposer_address" rows="3"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                                            placeholder="पूरा पता लिखें"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Declaration Section -->
                    <div class="bg-white rounded-lg shadow-lg p-6 md:p-8">
                        <h3 class="text-xl font-bold text-gray-800 mb-4">घोषणा पत्र</h3>
                        <div class="bg-gray-50 p-4 rounded-lg mb-6">
                            <p class="text-gray-700 leading-relaxed">
                                में घोषणा करता/करती हूँ कि उपरोक्त जानकारी सत्य है। असत्य पाये जाने की स्थिति में मेरी
                                सदस्यता समाप्त करने का अधिकार प्रबंधकारिणी समिति का होगा।
                            </p>
                        </div>

                        <div class="flex items-center mb-6">
                            <input type="checkbox" id="declaration" name="declaration" required
                                class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                            <label for="declaration" class="ml-2 text-gray-700">
                                मैं उपरोक्त घोषणा से सहमत हूँ <span class="text-red-500">*</span>
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="text-center">
                        <button type="submit"
                            class="bg-gradient-to-r from-teal-600 to-teal-700 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:from-teal-700 hover:to-teal-800 transform hover:scale-105 transition duration-200 shadow-lg">
                            आवेदन जमा करें
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Photo file validation
            const photoInput = document.getElementById('photo');
            if (photoInput) {
                photoInput.addEventListener('change', function(e) {
                    previewImage('photo');
                });
            }

            // Signature file validation
            const signatureInput = document.getElementById('signature');
            if (signatureInput) {
                signatureInput.addEventListener('change', function(e) {
                    previewImage('signature');
                });
            }
        });

        // Password validation
        document.getElementById('password_confirmation').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('पासवर्ड मेल नहीं खाते');
                this.classList.add('border-red-500');
            } else {
                this.setCustomValidity('');
                this.classList.remove('border-red-500');
            }
        });

        // Handle marital status change for main form
        document.getElementById('marital_status').addEventListener('change', function() {
            const vivahTithiField = document.getElementById('vivah_tithi_field');
            const spouseDetailsFields = document.getElementById('spouse_details_fields');

            if (this.value === 'विवाहित') {
                vivahTithiField.style.display = 'block';
                spouseDetailsFields.style.display = 'grid';
            } else {
                vivahTithiField.style.display = 'none';
                spouseDetailsFields.style.display = 'none';
                document.getElementById('vivah_tithi').value = '';
                document.getElementById('spouse_name').value = '';
                document.getElementById('spouse_education').value = '';
                document.getElementById('spouse_education_details').value = '';
                document.getElementById('spouse_work_details').value = '';
            }
        });

        // Initialize marital status on page load
        document.addEventListener('DOMContentLoaded', function() {
            const maritalStatus = document.getElementById('marital_status').value;
            if (maritalStatus === 'विवाहित') {
                document.getElementById('vivah_tithi_field').style.display = 'block';
                document.getElementById('spouse_details_fields').style.display = 'grid';
            }

            // Handle education qualification dropdown
            document.getElementById('education_qualification_id').addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                document.getElementById('education').value = selectedOption.text;
            });

            // Handle department dropdown
            document.getElementById('department_master_id').addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                document.getElementById('department_name').value = selectedOption.text;
            });

            // Handle office dropdown
            document.getElementById('office_master_id').addEventListener('change', function() {
                const selectedOption = this.options[this.selectedIndex];
                document.getElementById('office').value = selectedOption.text;
            });

            // Mobile number validation - only allow 10 digits
            document.getElementById('mobile_number').addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 10);
            });

            document.getElementById('mobile').addEventListener('input', function() {
                this.value = this.value.replace(/\D/g, '').substring(0, 10);
            });

            // Handle dependent dropdowns for location
            setupLocationDependency();
        });

        // Location dependency function
        function setupLocationDependency() {
            const divisionSelect = document.getElementById('division_master_id');
            const districtSelect = document.getElementById('district_master_id');
            const vikaskhandSelect = document.getElementById('vikaskhand_master_id');

            // Store original options
            const allDistricts = Array.from(districtSelect.options).slice(1); // Remove first option
            const allVikaskhands = Array.from(vikaskhandSelect.options).slice(1); // Remove first option

            // Division change handler
            divisionSelect.addEventListener('change', function() {
                const selectedDivisionId = this.value;

                // Clear and reset district dropdown
                districtSelect.innerHTML = '<option value="">जिला चुनें</option>';
                vikaskhandSelect.innerHTML = '<option value="">विकासखंड चुनें</option>';

                if (selectedDivisionId) {
                    // Get the division code from the selected option
                    const selectedDivisionOption = this.options[this.selectedIndex];
                    const divisionCode = selectedDivisionOption.dataset.divisionCode;

                    // Filter districts based on selected division code
                    allDistricts.forEach(option => {
                        if (option.dataset.divisionCode === divisionCode) {
                            districtSelect.appendChild(option.cloneNode(true));
                        }
                    });
                }
            });

            // District change handler
            districtSelect.addEventListener('change', function() {
                const selectedDistrictId = this.value;

                // Clear and reset vikaskhand dropdown
                vikaskhandSelect.innerHTML = '<option value="">विकासखंड चुनें</option>';

                if (selectedDistrictId) {
                    // Find the selected district's LGD code
                    const selectedDistrictOption = this.options[this.selectedIndex];
                    const districtLgdCode = selectedDistrictOption.dataset.districtLgd;

                    if (districtLgdCode) {
                        // Filter vikaskhands based on selected district LGD code
                        allVikaskhands.forEach(option => {
                            if (option.dataset.districtLgd === districtLgdCode) {
                                vikaskhandSelect.appendChild(option.cloneNode(true));
                            }
                        });
                    }
                }
            });
        }

        // Form validation and submission
        document.querySelector('form').addEventListener('submit', function (e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;

            // Check required fields
            requiredFields.forEach(field => {
                if (field.type === 'file') {
                    if (!field.files || field.files.length === 0) {
                        field.closest('.border-dashed').classList.add('border-red-500');
                        isValid = false;
                    } else {
                        field.closest('.border-dashed').classList.remove('border-red-500');
                    }
                } else if (!field.value.trim()) {
                    field.classList.add('border-red-500');
                    isValid = false;
                } else {
                    field.classList.remove('border-red-500');
                }
            });

            // Check password match
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('password_confirmation').value;

            if (password !== confirmPassword) {
                document.getElementById('password_confirmation').classList.add('border-red-500');
                isValid = false;
                alert('पासवर्ड मेल नहीं खाते। कृपया दोनों फील्ड में समान पासवर्ड दर्ज करें।');
            }

            // Validate file sizes one more time before submission
            const photoInput = document.getElementById('photo');
            const signatureInput = document.getElementById('signature');

            if (photoInput.files[0]) {
                const photoFile = photoInput.files[0];
                if (photoFile.size > 2 * 1024 * 1024) { // 2MB
                    alert('फोटो का साइज 2MB से कम होना चाहिए।');
                    isValid = false;
                }
            }

            if (signatureInput.files[0]) {
                const signatureFile = signatureInput.files[0];
                if (signatureFile.size > 1 * 1024 * 1024) { // 1MB
                    alert('हस्ताक्षर का साइज 1MB से कम होना चाहिए।');
                    isValid = false;
                }
            }

            if (!isValid) {
                e.preventDefault();
                if (password === confirmPassword) {
                    alert('कृपया सभी आवश्यक फील्ड भरें और फाइल साइज की जांच करें।');
                }
            } else {
                // Show loading state
                const submitBtn = this.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<span class="inline-flex items-center"><svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>सबमिट हो रहा है...</span>';
                }
            }
        });
    </script>




{{-- For Sucess Of Sadasyata --}}

{{-- <!-- Success Modal -->
<div id="successModal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-sm mx-auto border-dark border-2 border">
        <h2 class="text-xl font-bold text-success text-gray-800 mb-4">सदस्यता पंजीयन सफल रहा!</h2>
        <p class="text-gray-700 font-bold">आपका सदस्यता संख्या: <span class="text-danger" id="membershipNumber"></span></p>
    </div>
</div> --}}


<!-- Success Modal -->
<div id="successModal" class="fixed inset-0 flex items-center justify-center z-50 hidden">
    <div class="relative bg-white rounded-lg shadow-lg p-6 max-w-sm mx-auto border-dark border-2 border">
        
        <!-- Close Icon Button -->
        <button id="closeModal" class="absolute top-2 right-2 text-red-600 hover:text-red-800 text-xl font-bold">
            &times;
        </button>

        <h2 class="text-xl font-bold text-success text-gray-800 mb-4">सदस्यता पंजीयन सफल रहा!</h2>
        <p class="text-gray-700 font-bold mb-2">आपका सदस्यता संख्या: <span class="text-danger" id="membershipNumber"></span></p>
        <div class="bg-blue-50 p-3 rounded-lg mt-3">
            <p class="text-sm text-blue-800 font-semibold mb-1">लॉगिन जानकारी:</p>
            <p class="text-xs text-blue-700" id="loginInfo"></p>
            <p class="text-xs text-blue-600 mt-1">स्वीकृति के बाद आप सदस्य डैशबोर्ड में लॉगिन कर सकेंगे।</p>
        </div>
    </div>
</div>




<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Check if there is a success message
        @if(session('success'))
            // Get the membership number from the session
            const membershipNumber = "{{ session('membership_number') }}";
            const loginInfo = "{{ session('login_info') }}";

            document.getElementById('membershipNumber').textContent = membershipNumber;
            if (loginInfo) {
                document.getElementById('loginInfo').textContent = loginInfo;
            }
            document.getElementById('successModal').classList.remove('hidden');

            // Auto close the modal after 30 seconds
            setTimeout(() => {
                document.getElementById('successModal').classList.add('hidden');
            }, 30000);
        @endif

        // Close modal functionality
        document.getElementById('closeModal').addEventListener('click', function () {
            document.getElementById('successModal').classList.add('hidden');
        });
    });
</script>


@endsection