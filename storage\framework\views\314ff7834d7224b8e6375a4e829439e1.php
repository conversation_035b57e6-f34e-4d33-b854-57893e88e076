<?php $__env->startSection('title', 'प्रोफाइल'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50 dark:bg-navy-900">
    <!-- Mobile Header -->
    <div class="bg-white dark:bg-navy-800 shadow-sm border-b border-gray-200 dark:border-navy-700 lg:hidden">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <a href="<?php echo e(route('member.dashboard')); ?>" class="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-navy-700">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </a>
                    <h1 class="text-lg font-semibold text-gray-900 dark:text-white">प्रोफाइल</h1>
                </div>
            </div>
        </div>
    </div>

    <div class="px-4 sm:px-6 lg:px-8 py-6">
        <!-- Desktop Back Button -->
        <div class="hidden lg:block mb-6">
            <a href="<?php echo e(route('member.dashboard')); ?>" class="inline-flex items-center text-sm font-medium text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
                <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                डैशबोर्ड पर वापस जाएं
            </a>
        </div>

        <!-- Page Header -->
        <div class="mb-8 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
                    प्रोफाइल
                </h1>
                <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                    अपनी व्यक्तिगत जानकारी देखें और अपडेट करें
                </p>
            </div>
            <div class="mt-4 sm:mt-0">
                <a href="<?php echo e(route('member.profile.edit')); ?>" class="inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    प्रोफाइल संपादित करें
                </a>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Profile Photo & Basic Info -->
            <div class="lg:col-span-1">
                <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg p-6">
                    <div class="text-center">
                        <?php if($member->photo): ?>
                            <img class="mx-auto h-32 w-32 rounded-full object-cover" src="<?php echo e(Storage::url($member->photo)); ?>" alt="<?php echo e($member->name); ?>">
                        <?php else: ?>
                            <div class="mx-auto h-32 w-32 rounded-full bg-teal-100 dark:bg-teal-900 flex items-center justify-center">
                                <span class="text-4xl font-medium text-teal-600 dark:text-teal-300">
                                    <?php echo e(substr($member->name, 0, 1)); ?>

                                </span>
                            </div>
                        <?php endif; ?>
                        <h3 class="mt-4 text-xl font-semibold text-gray-900 dark:text-white"><?php echo e($member->name); ?></h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($member->membership_number); ?></p>
                        <div class="mt-3">
                            <span class="px-3 py-1 text-sm font-medium rounded-full <?php echo e($member->getStatusBadgeClass()); ?>">
                                <?php echo e($member->getStatusDisplayText()); ?>

                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Details -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">व्यक्तिगत जानकारी</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-6">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">नाम</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->name); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">पिता/पति का नाम</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->fathers_husband_name); ?></dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">मोबाइल नंबर</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->mobile_number); ?></dd>
                            </div>
                            <?php if($member->birth_date): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">जन्म तिथि</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->birth_date->format('d M Y')); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->marital_status): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">वैवाहिक स्थिति</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->marital_status); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->family_members): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">परिवार के सदस्य</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->family_members); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->education): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">शिक्षा</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->education); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->caste_details): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">जाति विवरण</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->caste_details); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->email): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">ईमेल</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->email); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->membershipVarg): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">सदस्यता वर्ग</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                                    <span class="inline-flex items-center px-2 py-1 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-400">
                                        <?php echo e($member->membershipVarg->name); ?>

                                    </span>
                                    <?php if($member->membershipVarg->fee): ?>
                                        <span class="text-gray-500 text-xs ml-2">- ₹<?php echo e(number_format($member->membershipVarg->fee, 2)); ?></span>
                                    <?php endif; ?>
                                </dd>
                            </div>
                            <?php endif; ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">आवेदन दिनांक</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->created_at->format('d M Y')); ?></dd>
                            </div>
                        </dl>

                        <?php if($member->address): ?>
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">पता</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->address); ?></dd>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Professional Information -->
                <?php if($member->department_name || $member->office): ?>
                <div class="mt-6 bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">व्यावसायिक जानकारी</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-6">
                            <?php if($member->department_name): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">विभाग का नाम</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->department_name); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->office): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">कार्यालय</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->office); ?></dd>
                            </div>
                            <?php endif; ?>
                        </dl>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Children Information -->
                <?php if($member->children && count($member->children) > 0): ?>
                <div class="mt-6 bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">बच्चों की जानकारी</h3>
                    </div>
                    <div class="px-6 py-4">
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-navy-700">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">नाम</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">लिंग</th>
                                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">जन्म तिथि</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200 dark:divide-navy-700">
                                    <?php $__currentLoopData = $member->children; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $child): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td class="px-4 py-2 text-sm text-gray-900 dark:text-white"><?php echo e($child['name'] ?? 'N/A'); ?></td>
                                        <td class="px-4 py-2 text-sm text-gray-900 dark:text-white"><?php echo e($child['gender'] ?? 'N/A'); ?></td>
                                        <td class="px-4 py-2 text-sm text-gray-900 dark:text-white">
                                            <?php echo e($child['dob'] ? \Carbon\Carbon::parse($child['dob'])->format('d M Y') : 'N/A'); ?>

                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Proposer Information -->
                <?php if($member->member_name_signature || $member->mobile || $member->proposer_address): ?>
                <div class="mt-6 bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">प्रस्तावक की जानकारी</h3>
                    </div>
                    <div class="px-6 py-4">
                        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-6">
                            <?php if($member->member_name_signature): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">सदस्य का नाम</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->member_name_signature); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->vibhag): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">विभाग</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->vibhag); ?></dd>
                            </div>
                            <?php endif; ?>
                            <?php if($member->mobile): ?>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">मोबाइल</dt>
                                <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->mobile); ?></dd>
                            </div>
                            <?php endif; ?>
                        </dl>
                        <?php if($member->proposer_address): ?>
                        <div class="mt-6">
                            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">पता</dt>
                            <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?php echo e($member->proposer_address); ?></dd>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Vanshavali (Ancestors) Information -->
                <?php if($member->ancestors && count($member->ancestors) > 0): ?>
                <div class="mt-6 bg-white dark:bg-navy-800 shadow-sm rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200 dark:border-navy-700">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">पारिवारिक वंशावली</h3>
                            <a href="<?php echo e(route('member.family-tree')); ?>" class="text-sm text-teal-600 hover:text-teal-700 dark:text-teal-400 dark:hover:text-teal-300">
                                विस्तृत वंशावली देखें →
                            </a>
                        </div>
                    </div>
                    <div class="px-6 py-4">
                        <?php
                            $sortedAncestors = collect($member->ancestors)->sortByDesc('generation_level');
                            $generationLabels = [
                                7 => '7वीं पीढ़ी',
                                6 => '6वीं पीढ़ी',
                                5 => '5वीं पीढ़ी',
                                4 => '4वीं पीढ़ी',
                                3 => '3वीं पीढ़ी',
                                2 => '2वीं पीढ़ी (दादा-दादी)',
                                1 => '1वीं पीढ़ी (माता-पिता)'
                            ];
                        ?>

                        <?php $__currentLoopData = $generationLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $level => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $ancestorsInLevel = $sortedAncestors->where('generation_level', $level);
                            ?>
                            <?php if($ancestorsInLevel->count() > 0): ?>
                                <div class="mb-6">
                                    <h4 class="text-md font-semibold text-gray-800 dark:text-gray-200 mb-3 pb-2 border-b border-gray-200 dark:border-navy-600">
                                        <?php echo e($label); ?>

                                    </h4>
                                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <?php $__currentLoopData = $ancestorsInLevel; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ancestor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="border border-gray-200 dark:border-navy-600 rounded-lg p-4 bg-gray-50 dark:bg-navy-700">
                                                <h5 class="font-bold text-gray-900 dark:text-white mb-2">
                                                    <?php echo e($ancestor['name'] ?? 'N/A'); ?>

                                                </h5>
                                                <div class="text-sm space-y-1 text-gray-600 dark:text-gray-300">
                                                    <?php if(!empty($ancestor['birth_date'])): ?>
                                                        <div><strong>जन्म:</strong> <?php echo e(\Carbon\Carbon::parse($ancestor['birth_date'])->format('d/m/Y')); ?></div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['death_date'])): ?>
                                                        <div><strong>मृत्यु:</strong> <?php echo e(\Carbon\Carbon::parse($ancestor['death_date'])->format('d/m/Y')); ?></div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['birth_place'])): ?>
                                                        <div><strong>जन्म स्थान:</strong> <?php echo e($ancestor['birth_place']); ?></div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['occupation'])): ?>
                                                        <div><strong>व्यवसाय:</strong> <?php echo e($ancestor['occupation']); ?></div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['spouse'])): ?>
                                                        <div><strong>पत्नी/पति:</strong> <?php echo e($ancestor['spouse']); ?></div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['children_count'])): ?>
                                                        <div><strong>संतान:</strong> <?php echo e($ancestor['children_count']); ?></div>
                                                    <?php endif; ?>
                                                    <?php if(!empty($ancestor['gotra'])): ?>
                                                        <div><strong>गोत्र:</strong> <?php echo e($ancestor['gotra']); ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.member', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\web-yadav-samaj\resources\views/member/profile.blade.php ENDPATH**/ ?>